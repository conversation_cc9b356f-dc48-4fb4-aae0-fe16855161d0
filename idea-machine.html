<!DOCTYPE html>
<html>
<head>
<title>Random Word Pair</title>
<style>
body {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  margin: 0;
  font-family: "SF Mono", monospace;
  transition: background-color 0.5s ease, filter 0.5s ease, opacity 0.5s ease;
  cursor: pointer;
  opacity: 0;
  filter: blur(10px);
}

body.loaded {
  opacity: 1;
  filter: blur(0px);
}

.word-container {
  display: flex;
  gap: 20px;
  text-align: center;
}

.word {
  font-size: 5em;
  font-weight: 400;
  transition: color 0.5s ease;
}

#regenerateButton {
  position: absolute;
  bottom: 20px;
  left: 20px;
  padding: 10px 20px;
  background-color: #4CAF50; /* Green */
  color: white;
  border: none;
  cursor: pointer;
  border-radius: 5px;
  opacity: 0;           /* Initially hidden */
  transition: opacity 0.3s ease; /* Fade-in effect */
}

body:hover #regenerateButton {
  opacity: 1;           /* Show on hover */
}

#regenerateButton:hover {
  background-color: #3e8e41;
}
</style>
</head>
<body>

<div class="word-container">
  <span id="word1" class="word"></span>
  <span id="word2" class="word"></span>
</div>

<button id="regenerateButton">Regenerate Colors</button>

<script>
// Configuration
const adjectives = [
  "modular", "network", "friendly", "user-focused", "generative",
  "automated", "parametric", "visual", "Open", "eco", "DIY",
  "user-friendly" //added
];

const nouns = [
  "LIDAR", "automation", "workflow", "art", "music", "design", "UI",
  "sensor", "tracking", "analysis", "maths", "interface", "physics",
  "vector", "machine", "hardware", "learning", "computer", "data",
  "origami", "map", "type", "language", "audio", "3d", "video",
  "colour", "printing", "medicine", "algorithm", "font", "sketch",
  "notes", "maker", "script", "plugin"
];

const foregroundColors = [
  "#FF5733",  // Bright Orange-Red
  "#33FF57",   // Bright Green
  "#FFC300",   // Bright Yellow
  "#3498DB",   // Bright Blue
  "#A7D1AB",  // Soft Green
  "#F2D7D5",   // Soft Pink
  "#C8B6E2"    // Soft Purple
];

const backgroundColors = [
  "#F0F8FF",  // AliceBlue
  "#E6E6FA",  // Lavender
  "#F5F5DC",   // Beige
  "#FFF0F5",  // LavenderBlush - Soft Pink
  "#E0FFFF"   // LightCyan - Soft Blue
];

function chooseRandom(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function updateWords() {
    const word1Element = document.getElementById("word1");
    const word2Element = document.getElementById("word2");

    let randomWord1;
    let randomWord2;

    // Randomly choose whether to use an adjective or a noun for the first word
    if (Math.random() < 0.5) { // 50% chance of adjective
        randomWord1 = chooseRandom(adjectives);
    } else {
        randomWord1 = chooseRandom(nouns);
    }

    // Always choose a noun for the second word
    randomWord2 = chooseRandom(nouns);


    word1Element.textContent = randomWord1;
    word2Element.textContent = randomWord2;

    updateColors(); // Call updateColors after words are set
}


function updateColors() {
  const word1Element = document.getElementById("word1");
  const word2Element = document.getElementById("word2");

  const backgroundColor = chooseRandom(backgroundColors);
  let foregroundColor1 = chooseRandom(foregroundColors);
  let foregroundColor2 = chooseRandom(foregroundColors);

    // Ensure the foreground colors are different
    while (foregroundColor2 === foregroundColor1) {
        foregroundColor2 = chooseRandom(foregroundColors);
    }

  word1Element.style.color = foregroundColor1;
  word2Element.style.color = foregroundColor2;
  document.body.style.backgroundColor = backgroundColor;
}

// Initial update with fade-in effect
window.onload = () => {
    updateWords();
    // Add fade-in effect after a brief delay
    setTimeout(() => {
        document.body.classList.add('loaded');
    }, 100);
};

// Make entire page clickable to regenerate words
document.body.addEventListener("click", (e) => {
    // Prevent button click from triggering page click
    if (e.target.id !== "regenerateButton") {
        updateWords();
    }
});

// Regenerate colors button (keep existing functionality)
document.getElementById("regenerateButton").addEventListener("click", updateColors);

</script>

</body>
</html>
